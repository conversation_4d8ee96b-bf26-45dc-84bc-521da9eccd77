"""
Authentication Service

This module provides basic authentication services for the Jenkins Reader Agent
multi-agent system.

Author: <PERSON><PERSON><PERSON>
Version: 1.0.0
"""

from typing import Optional, Dict, Any
from datetime import datetime, timedelta
import secrets
import hashlib

from .models import User, AuthToken, RefreshToken, RoleType
from .exceptions import AuthenticationError, InvalidTokenError, TokenExpiredError


class OAuth2Provider:
    """OAuth2 provider for Google authentication."""
    
    def __init__(self, client_id: str, client_secret: str, redirect_uri: str):
        self.client_id = client_id
        self.client_secret = client_secret
        self.redirect_uri = redirect_uri
    
    def get_authorization_url(self, state: Optional[str] = None) -> str:
        """Generate OAuth2 authorization URL."""
        return f"https://accounts.google.com/o/oauth2/v2/auth?client_id={self.client_id}"


class JWTManager:
    """JWT token manager for internal authentication."""
    
    def __init__(self, secret_key: str, algorithm: str = "HS256", 
                 access_token_expire_minutes: int = 30):
        self.secret_key = secret_key
        self.algorithm = algorithm
        self.access_token_expire_minutes = access_token_expire_minutes
    
    def create_access_token(self, user: User) -> AuthToken:
        """Create JWT access token for user."""
        expires_at = datetime.utcnow() + timedelta(minutes=self.access_token_expire_minutes)
        
        # Simple token generation for testing
        token_data = f"{user.id}:{user.email}:{user.role.value}:{expires_at.isoformat()}"
        token = hashlib.sha256(f"{token_data}:{self.secret_key}".encode()).hexdigest()
        
        return AuthToken(
            access_token=token,
            expires_in=self.access_token_expire_minutes * 60,
            expires_at=expires_at
        )
    
    def verify_token(self, token: str) -> Dict[str, Any]:
        """Verify and decode JWT token."""
        # Simple verification for testing
        if len(token) != 64:  # SHA256 hex length
            raise InvalidTokenError("Invalid token format")
        
        # In a real implementation, this would decode the JWT
        return {"sub": "test_user", "email": "<EMAIL>", "role": "viewer"}


class TokenValidator:
    """Token validation utilities."""
    
    def __init__(self, jwt_manager: JWTManager):
        self.jwt_manager = jwt_manager
    
    def validate_access_token(self, token: str) -> Dict[str, Any]:
        """Validate access token and return claims."""
        return self.jwt_manager.verify_token(token)


class AuthenticationService:
    """Main authentication service."""
    
    def __init__(self, oauth2_provider: OAuth2Provider, jwt_manager: JWTManager):
        self.oauth2_provider = oauth2_provider
        self.jwt_manager = jwt_manager
        self.token_validator = TokenValidator(jwt_manager)
        
        # In-memory user store for testing
        self._users: Dict[str, User] = {}
    
    async def validate_token(self, token: str) -> User:
        """Validate access token and return user."""
        claims = self.token_validator.validate_access_token(token)
        
        # Create a test user for demonstration
        user = User(
            email=claims.get("email", "<EMAIL>"),
            name="Test User",
            role=RoleType.VIEWER
        )
        
        return user
    
    def create_test_user(self, email: str, name: str, role: RoleType) -> User:
        """Create a test user."""
        user = User(email=email, name=name, role=role)
        self._users[user.id] = user
        return user
