"""
Validation Exceptions

This module defines custom exceptions for input validation and security
violations in the Jenkins Reader Agent.

Author: <PERSON><PERSON><PERSON>
Version: 1.0.0
"""

from typing import Optional, Dict, Any, List
from fastapi import status


class ValidationError(Exception):
    """Base exception for validation errors."""
    
    def __init__(
        self,
        message: str = "Validation failed",
        field: Optional[str] = None,
        value: Optional[Any] = None,
        status_code: int = status.HTTP_422_UNPROCESSABLE_ENTITY,
        details: Optional[Dict[str, Any]] = None
    ):
        self.message = message
        self.field = field
        self.value = value
        self.status_code = status_code
        self.details = details or {}
        super().__init__(self.message)


class SecurityViolationError(ValidationError):
    """Raised when input violates security policies."""
    
    def __init__(
        self,
        message: str = "Security violation detected",
        violation_type: Optional[str] = None,
        field: Optional[str] = None,
        value: Optional[Any] = None
    ):
        super().__init__(
            message=message,
            field=field,
            value=value,
            status_code=status.HTTP_400_BAD_REQUEST,
            details={
                "violation_type": violation_type,
                "security_error": True
            }
        )


class InvalidInputError(ValidationError):
    """Raised for invalid input data."""
    
    def __init__(
        self,
        message: str = "Invalid input",
        field: Optional[str] = None,
        value: Optional[Any] = None,
        expected_format: Optional[str] = None
    ):
        super().__init__(
            message=message,
            field=field,
            value=value,
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
            details={"expected_format": expected_format}
        )


class URLValidationError(ValidationError):
    """Raised for URL validation errors."""
    
    def __init__(
        self,
        message: str = "Invalid URL",
        url: Optional[str] = None,
        reason: Optional[str] = None
    ):
        super().__init__(
            message=message,
            field="url",
            value=url,
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
            details={"reason": reason}
        )


class SSRFViolationError(SecurityViolationError):
    """Raised when URL could lead to SSRF attack."""
    
    def __init__(
        self,
        message: str = "Potential SSRF attack detected",
        url: Optional[str] = None,
        blocked_reason: Optional[str] = None
    ):
        super().__init__(
            message=message,
            violation_type="SSRF",
            field="url",
            value=url
        )
        self.details["blocked_reason"] = blocked_reason


class XSSViolationError(SecurityViolationError):
    """Raised when input contains potential XSS payload."""
    
    def __init__(
        self,
        message: str = "Potential XSS payload detected",
        field: Optional[str] = None,
        value: Optional[str] = None
    ):
        super().__init__(
            message=message,
            violation_type="XSS",
            field=field,
            value=value
        )


class SQLInjectionViolationError(SecurityViolationError):
    """Raised when input contains potential SQL injection."""
    
    def __init__(
        self,
        message: str = "Potential SQL injection detected",
        field: Optional[str] = None,
        value: Optional[str] = None
    ):
        super().__init__(
            message=message,
            violation_type="SQL_INJECTION",
            field=field,
            value=value
        )


class FilenameViolationError(SecurityViolationError):
    """Raised for unsafe filename patterns."""
    
    def __init__(
        self,
        message: str = "Unsafe filename detected",
        filename: Optional[str] = None,
        reason: Optional[str] = None
    ):
        super().__init__(
            message=message,
            violation_type="UNSAFE_FILENAME",
            field="filename",
            value=filename
        )
        self.details["reason"] = reason


class InputTooLargeError(ValidationError):
    """Raised when input exceeds size limits."""
    
    def __init__(
        self,
        message: str = "Input too large",
        field: Optional[str] = None,
        size: Optional[int] = None,
        max_size: Optional[int] = None
    ):
        super().__init__(
            message=message,
            field=field,
            status_code=status.HTTP_413_REQUEST_ENTITY_TOO_LARGE,
            details={
                "size": size,
                "max_size": max_size
            }
        )


class InvalidFormatError(ValidationError):
    """Raised for format validation errors."""
    
    def __init__(
        self,
        message: str = "Invalid format",
        field: Optional[str] = None,
        value: Optional[Any] = None,
        expected_format: Optional[str] = None,
        pattern: Optional[str] = None
    ):
        super().__init__(
            message=message,
            field=field,
            value=value,
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
            details={
                "expected_format": expected_format,
                "pattern": pattern
            }
        )


class MultipleValidationErrors(ValidationError):
    """Raised when multiple validation errors occur."""
    
    def __init__(
        self,
        message: str = "Multiple validation errors",
        errors: List[ValidationError] = None
    ):
        self.errors = errors or []
        error_details = [
            {
                "field": error.field,
                "message": error.message,
                "value": error.value,
                "details": error.details
            }
            for error in self.errors
        ]
        
        super().__init__(
            message=message,
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
            details={"errors": error_details}
        )


class ConfigurationValidationError(ValidationError):
    """Raised for configuration validation errors."""
    
    def __init__(
        self,
        message: str = "Configuration validation failed",
        config_key: Optional[str] = None,
        config_value: Optional[Any] = None
    ):
        super().__init__(
            message=message,
            field=config_key,
            value=config_value,
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            details={"config_error": True}
        )
