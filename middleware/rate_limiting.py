"""
Rate Limiting Middleware

This module provides rate limiting functionality for the Jenkins Reader Agent
to prevent abuse and ensure fair usage of resources.

Author: <PERSON><PERSON><PERSON>
Version: 1.0.0
"""

import time
import asyncio
from typing import Dict, Optional, Callable, Any
from collections import defaultdict, deque
from dataclasses import dataclass
from datetime import datetime, timedelta

from fastapi import Request, HTTPException, status
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.responses import Response
import logging

logger = logging.getLogger(__name__)


@dataclass
class RateLimitConfig:
    """Configuration for rate limiting."""
    requests_per_minute: int = 60
    requests_per_hour: int = 1000
    requests_per_day: int = 10000
    burst_limit: int = 10
    window_size: int = 60  # seconds


class RateLimiter:
    """Token bucket rate limiter implementation."""
    
    def __init__(self, config: RateLimitConfig):
        self.config = config
        self.buckets: Dict[str, Dict[str, Any]] = defaultdict(dict)
        self.request_history: Dict[str, deque] = defaultdict(lambda: deque(maxlen=1000))
        
    def _get_client_key(self, request: Request) -> str:
        """Get unique client identifier."""
        # Try to get user ID from request state (if authenticated)
        user = getattr(request.state, 'user', None)
        if user and hasattr(user, 'id'):
            return f"user:{user.id}"
        
        # Fall back to IP address
        forwarded_for = request.headers.get("X-Forwarded-For")
        if forwarded_for:
            client_ip = forwarded_for.split(",")[0].strip()
        else:
            client_ip = request.client.host if request.client else "unknown"
        
        return f"ip:{client_ip}"
    
    def _init_bucket(self, client_key: str) -> None:
        """Initialize rate limit bucket for client."""
        now = time.time()
        self.buckets[client_key] = {
            'tokens': self.config.burst_limit,
            'last_refill': now,
            'minute_count': 0,
            'minute_start': now,
            'hour_count': 0,
            'hour_start': now,
            'day_count': 0,
            'day_start': now,
        }
    
    def _refill_bucket(self, client_key: str) -> None:
        """Refill tokens in the bucket based on elapsed time."""
        now = time.time()
        bucket = self.buckets[client_key]
        
        # Calculate tokens to add based on elapsed time
        elapsed = now - bucket['last_refill']
        tokens_to_add = elapsed * (self.config.requests_per_minute / 60.0)
        
        # Add tokens but don't exceed burst limit
        bucket['tokens'] = min(
            self.config.burst_limit,
            bucket['tokens'] + tokens_to_add
        )
        bucket['last_refill'] = now
        
        # Reset counters if time windows have passed
        if now - bucket['minute_start'] >= 60:
            bucket['minute_count'] = 0
            bucket['minute_start'] = now
        
        if now - bucket['hour_start'] >= 3600:
            bucket['hour_count'] = 0
            bucket['hour_start'] = now
        
        if now - bucket['day_start'] >= 86400:
            bucket['day_count'] = 0
            bucket['day_start'] = now
    
    def is_allowed(self, request: Request) -> tuple[bool, Dict[str, Any]]:
        """Check if request is allowed based on rate limits."""
        client_key = self._get_client_key(request)
        
        # Initialize bucket if it doesn't exist
        if client_key not in self.buckets:
            self._init_bucket(client_key)
        
        # Refill bucket
        self._refill_bucket(client_key)
        
        bucket = self.buckets[client_key]
        now = time.time()
        
        # Check various rate limits
        limits_info = {
            'client_key': client_key,
            'tokens_remaining': int(bucket['tokens']),
            'minute_requests': bucket['minute_count'],
            'hour_requests': bucket['hour_count'],
            'day_requests': bucket['day_count'],
            'reset_time': bucket['last_refill'] + 60,
        }
        
        # Check burst limit (token bucket)
        if bucket['tokens'] < 1:
            logger.warning(f"Rate limit exceeded (burst): {client_key}")
            return False, limits_info
        
        # Check per-minute limit
        if bucket['minute_count'] >= self.config.requests_per_minute:
            logger.warning(f"Rate limit exceeded (per minute): {client_key}")
            return False, limits_info
        
        # Check per-hour limit
        if bucket['hour_count'] >= self.config.requests_per_hour:
            logger.warning(f"Rate limit exceeded (per hour): {client_key}")
            return False, limits_info
        
        # Check per-day limit
        if bucket['day_count'] >= self.config.requests_per_day:
            logger.warning(f"Rate limit exceeded (per day): {client_key}")
            return False, limits_info
        
        # Consume token and increment counters
        bucket['tokens'] -= 1
        bucket['minute_count'] += 1
        bucket['hour_count'] += 1
        bucket['day_count'] += 1
        
        # Record request in history
        self.request_history[client_key].append(now)
        
        logger.debug(f"Request allowed for {client_key}: {limits_info}")
        return True, limits_info
    
    def get_retry_after(self, client_key: str) -> int:
        """Get retry-after time in seconds."""
        if client_key not in self.buckets:
            return 60
        
        bucket = self.buckets[client_key]
        now = time.time()
        
        # Calculate time until next token is available
        if bucket['tokens'] < 1:
            time_for_token = (1 - bucket['tokens']) / (self.config.requests_per_minute / 60.0)
            return max(1, int(time_for_token))
        
        # Calculate time until minute window resets
        minute_reset = 60 - (now - bucket['minute_start'])
        return max(1, int(minute_reset))


class RateLimitingMiddleware(BaseHTTPMiddleware):
    """Middleware for rate limiting requests."""
    
    def __init__(
        self,
        app,
        config: Optional[RateLimitConfig] = None,
        excluded_paths: Optional[list] = None
    ):
        super().__init__(app)
        self.config = config or RateLimitConfig()
        self.rate_limiter = RateLimiter(self.config)
        self.excluded_paths = excluded_paths or [
            "/health",
            "/docs",
            "/redoc",
            "/openapi.json",
            "/static"
        ]
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """Process request through rate limiting middleware."""
        # Skip rate limiting for excluded paths
        if any(request.url.path.startswith(path) for path in self.excluded_paths):
            return await call_next(request)
        
        # Check rate limits
        allowed, limits_info = self.rate_limiter.is_allowed(request)
        
        if not allowed:
            # Get retry-after time
            retry_after = self.rate_limiter.get_retry_after(limits_info['client_key'])
            
            # Log rate limit violation
            logger.warning(
                f"Rate limit exceeded for {limits_info['client_key']} "
                f"on {request.method} {request.url.path}"
            )
            
            # Return rate limit error
            return Response(
                content='{"detail": "Rate limit exceeded", "retry_after": ' + str(retry_after) + '}',
                status_code=status.HTTP_429_TOO_MANY_REQUESTS,
                headers={
                    "Retry-After": str(retry_after),
                    "X-RateLimit-Limit": str(self.config.requests_per_minute),
                    "X-RateLimit-Remaining": str(limits_info['tokens_remaining']),
                    "X-RateLimit-Reset": str(int(limits_info['reset_time'])),
                    "Content-Type": "application/json"
                }
            )
        
        # Process request
        response = await call_next(request)
        
        # Add rate limit headers to response
        response.headers["X-RateLimit-Limit"] = str(self.config.requests_per_minute)
        response.headers["X-RateLimit-Remaining"] = str(limits_info['tokens_remaining'])
        response.headers["X-RateLimit-Reset"] = str(int(limits_info['reset_time']))
        
        return response


# Decorator for applying rate limits to specific endpoints
def rate_limit(
    requests_per_minute: int = 60,
    requests_per_hour: int = 1000,
    requests_per_day: int = 10000
):
    """Decorator to apply rate limiting to specific endpoints."""
    def decorator(func):
        async def wrapper(*args, **kwargs):
            # This would be implemented with FastAPI dependencies
            # For now, it's a placeholder
            return await func(*args, **kwargs)
        return wrapper
    return decorator


# Utility functions
def create_rate_limit_config(
    requests_per_minute: int = 60,
    requests_per_hour: int = 1000,
    requests_per_day: int = 10000,
    burst_limit: int = 10
) -> RateLimitConfig:
    """Create a rate limit configuration."""
    return RateLimitConfig(
        requests_per_minute=requests_per_minute,
        requests_per_hour=requests_per_hour,
        requests_per_day=requests_per_day,
        burst_limit=burst_limit
    )


def get_default_rate_limits() -> Dict[str, RateLimitConfig]:
    """Get default rate limit configurations for different user types."""
    return {
        "anonymous": RateLimitConfig(
            requests_per_minute=10,
            requests_per_hour=100,
            requests_per_day=1000,
            burst_limit=5
        ),
        "authenticated": RateLimitConfig(
            requests_per_minute=60,
            requests_per_hour=1000,
            requests_per_day=10000,
            burst_limit=10
        ),
        "premium": RateLimitConfig(
            requests_per_minute=120,
            requests_per_hour=5000,
            requests_per_day=50000,
            burst_limit=20
        ),
        "admin": RateLimitConfig(
            requests_per_minute=300,
            requests_per_hour=10000,
            requests_per_day=100000,
            burst_limit=50
        )
    }
